# JianYing MCP - 剪映视频制作 MCP 服务器

[![Python](https://img.shields.io/badge/Python-3.13+-blue.svg)](https://python.org)
[![MCP](https://img.shields.io/badge/MCP-Compatible-green.svg)](https://modelcontextprotocol.io)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 Model Context Protocol (MCP) 的剪映视频制作自动化工具，让 AI 助手能够通过自然语言创建专业的视频内容。

## 🎯 项目简介

JianYing MCP 是一个强大的视频制作自动化工具，通过 MCP 协议让 AI 助手（如 Claude）能够：

- 🎬 **自动创建剪映草稿项目**
- 🎵 **智能添加音频、视频、文本素材**
- ✨ **应用各种特效、滤镜、动画**
- 🎨 **自动化视频编辑流程**
- 📤 **导出为剪映可编辑的项目文件**

## 🚀 核心功能

### 📋 草稿管理
- `create_draft` - 创建新的视频草稿项目
- `export_draft` - 导出为剪映项目文件

### 🛤️ 轨道管理
- `create_track` - 创建视频/音频/文本轨道
- 支持多轨道并行编辑

### 🎥 视频处理
- `add_video_segment` - 添加视频片段
- `add_video_animation` - 添加入场/出场动画
- `add_video_transition` - 添加转场效果
- `add_video_filter` - 应用滤镜效果
- `add_video_mask` - 添加蒙版效果
- `add_video_background_filling` - 背景填充
- `add_video_keyframe` - 关键帧动画

### 🎵 音频处理
- `add_audio_segment` - 添加音频片段
- `add_audio_effect` - 音频特效（电音、混响等）
- `add_audio_fade` - 淡入淡出效果
- `add_audio_keyframe` - 音频关键帧

### 📝 文本处理
- `add_text_segment` - 添加文本片段
- `add_text_animation` - 文字动画效果
- `add_text_bubble` - 文字气泡效果
- `add_text_effect` - 文字花字特效

### 🔧 实用工具
- `parse_media_info` - 解析媒体文件信息
- `find_effects_by_type` - 查找可用特效资源

## 📦 安装部署

### 前置要求

- Python 3.13+
- [uv](https://docs.astral.sh/uv/) 包管理器
- 支持 MCP 的 AI 客户端（如 Claude Desktop）

### 1. 安装 uv

**Windows:**
```bash
# 使用 PowerShell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**macOS/Linux:**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 克隆项目

```bash
git clone https://github.com/your-username/jianying-mcp.git
cd jianying-mcp
```

### 3. 安装依赖

```bash
# 进入项目目录
cd jianying-mcp

# 安装项目依赖
uv sync

# 或者如果没有 uv.lock 文件，使用
uv install
```

### 4. 配置环境变量

创建 `.env` 文件并配置路径：

```bash
# 数据存储路径 - 存储草稿的操作数据
SAVE_PATH=D:\your-path\jianying-mcp\data\draft

# 导出路径 - 生成的剪映草稿文件存放位置
OUTPUT_PATH=D:\your-path\jianying-mcp\data\output
```

> 💡 **提示**: 请确保这两个目录存在，如果不存在请手动创建

### 5. 配置 MCP 客户端

在你的 MCP 客户端（如 Claude Desktop）配置文件中添加以下内容：

**Claude Desktop 配置路径:**
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

**配置内容:**
```json
{
  "mcpServers": {
    "jianying-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "D:/your-path/jianying-mcp/jianyingdraft",
        "run",
        "server.py"
      ],
      "env": {
        "SAVE_PATH": "D:\\your-path\\jianying-mcp\\data\\draft",
        "OUTPUT_PATH": "D:\\your-path\\jianying-mcp\\data\\output"
      }
    }
  }
}
```

> ⚠️ **注意**: 请将路径替换为你的实际项目路径

### 6. 重启客户端

重启 Claude Desktop 或你的 MCP 客户端，即可开始使用！

## 🎬 使用示例

### 基础视频制作流程

```
1. 创建草稿: "帮我创建一个名为'我的第一个视频'的草稿"
2. 创建轨道: "创建一个视频轨道和一个音频轨道"
3. 添加素材: "添加视频文件到轨道，时间范围0s-10s"
4. 应用特效: "给视频添加淡入动画效果"
5. 导出项目: "导出草稿到剪映"
```

### 高级功能示例

```
- "添加背景音乐，并设置淡入淡出效果"
- "给文字添加打字机动画，持续2秒"
- "应用复古滤镜，强度设为80%"
- "添加圆形蒙版，中心位置在(0.5, 0.3)"
```

## 📁 项目结构

```
jianying-mcp/
├── jianyingdraft/           # 核心代码目录
│   ├── tool/               # MCP 工具定义
│   │   ├── draft_tool.py   # 草稿管理工具
│   │   ├── track_tool.py   # 轨道管理工具
│   │   ├── video_tool.py   # 视频处理工具
│   │   ├── audio_tool.py   # 音频处理工具
│   │   ├── text_tool.py    # 文本处理工具
│   │   └── utility_tool.py # 实用工具
│   ├── services/           # 业务逻辑层
│   ├── jianying/          # 剪映项目处理
│   ├── utils/             # 工具函数
│   └── server.py          # MCP 服务器入口
├── data/                  # 数据目录
│   ├── draft/            # 草稿数据存储
│   └── output/           # 导出文件
├── pyproject.toml         # 项目配置文件
├── .env                   # 环境变量配置
└── README.md
```

## 🔧 开发指南

### 本地开发

```bash
# 安装依赖
uv sync

# 运行服务器
uv run jianyingdraft/server.py

# 运行测试
uv run pytest
```

### 调试模式

使用 MCP Inspector 进行调试：

```bash
uv run mcp dev jianyingdraft/server.py
```

## 🙏 致谢

- [Model Context Protocol](https://modelcontextprotocol.io) - 提供了强大的 AI 集成协议
- [pyJianYingDraft](https://github.com/your-repo/pyJianYingDraft) - 剪映项目文件处理库


⭐ 如果这个项目对你有帮助，请给个 Star 支持一下！