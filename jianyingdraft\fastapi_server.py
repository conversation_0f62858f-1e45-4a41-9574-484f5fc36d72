# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>
Create Time: 2025/8/14 下午4:15
File Name:fastapi_server.py
"""
import os
import zipfile
import tempfile
from pathlib import Path

from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import FileResponse

from utils.index_manager import IndexManager

load_dotenv()

# 获取环境变量
SAVE_PATH = os.getenv('SAVE_PATH')
ZIP_PATH = os.getenv('ZIP_PATH')
OUTPUT_PATH = os.getenv('OUTPUT_PATH')

app = FastAPI(title="JianYing Draft Export API", version="1.0.0")


@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {"message": "JianYing Draft Export API", "version": "1.0.0"}


@app.get("/export/zip")
async def export_draft_to_zip(draft_id: str = Query(..., description="草稿ID")):
    """
    导出草稿文件夹并打包为zip文件

    Args:
        draft_id: 草稿ID (36位UUID格式)

    Returns:
        zip文件的下载响应
    """
    # 验证draft_id格式
    if len(draft_id) != 36:
        raise HTTPException(status_code=400, detail="Draft ID is invalid")

    # 获取草稿信息
    index_manager = IndexManager()
    try:
        draft_info = index_manager.get_draft_info(draft_id)
        draft_name = draft_info['draft_name']
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Draft not found: {draft_id}")

    folder_path = os.path.join(OUTPUT_PATH,draft_name)
    # 判断output_path是否存在
    if not os.path.exists(folder_path):
        raise HTTPException(status_code=404, detail=f"Draft not found: {draft_id}")
    # 转换为绝对路径
    abs_folder_path = Path(folder_path).resolve()

    try:
        # 确保ZIP_PATH目录存在
        os.makedirs(ZIP_PATH, exist_ok=True)

        # 在ZIP_PATH目录下创建临时zip文件
        zip_filename = f"{draft_name}.zip"
        temp_zip_path = os.path.join(ZIP_PATH, f"temp_{draft_id}_{zip_filename}")

        # 创建zip文件
        with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 遍历文件夹中的所有文件和子文件夹
            for root, dirs, files in os.walk(abs_folder_path):
                for file in files:
                    file_path = Path(root) / file
                    # 计算相对路径，用于zip文件内的路径
                    arcname = file_path.relative_to(abs_folder_path)
                    zipf.write(file_path, arcname)

        # 返回文件响应
        return FileResponse(
            path=temp_zip_path,
            filename=zip_filename,
            media_type='application/zip',
            background=_cleanup_temp_file(temp_zip_path)
        )

    except Exception as e:
        # 清理临时文件
        if 'temp_zip_path' in locals() and os.path.exists(temp_zip_path):
            os.unlink(temp_zip_path)
        raise HTTPException(status_code=500, detail=f"创建zip文件失败: {str(e)}")


def _cleanup_temp_file(file_path: str):
    """清理临时文件的后台任务"""
    import asyncio

    async def cleanup():
        # 等待一段时间后删除临时文件
        await asyncio.sleep(10)
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception:
            pass  # 忽略删除失败的情况
        # 清空output下的文件
        for root, dirs, files in os.walk(OUTPUT_PATH):
            for file in files:
                os.unlink(os.path.join(root, file))

    return cleanup


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8098)
