# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
File Name:draft_tool.py
"""
from mcp.server.fastmcp import FastMCP
from jianyingdraft.jianying.export import ExportDraft
from jianyingdraft.utils.response import ToolResponse
from jianyingdraft.utils.index_manager import index_manager
from typing import Optional
import uuid
import json
import os
from dotenv import load_dotenv
import datetime

# 加载环境变量
load_dotenv()

# 获取环境变量
SAVE_PATH = os.getenv('SAVE_PATH')

def draft_tools(mcp: FastMCP):
    @mcp.tool()
    def create_draft(draft_name: str, width: int = 1920, height: int = 1080, fps: int = 30):
        """
        创建草稿

        Args:
            draft_name:  str 草稿名称
            width: int,视频宽度,默认1920
            height: int，视频高度，默认1080
            fps: int，帧率，默认30
        """
        # 验证SAVE_PATH是否存在
        if not os.path.exists(SAVE_PATH):
            raise FileNotFoundError(f"草稿存储路径不存在: {SAVE_PATH}")
        # 生成草稿ID
        draft_id = str(uuid.uuid4())
        # 构建完整的草稿路径
        draft_path = os.path.join(SAVE_PATH, draft_id)
        # 创建草稿数据
        draft_data = {
            "draft_id": draft_id,
            "draft_name": draft_name,
            "width": width,
            "height": height,
            "fps": fps
        }
        # 在SAVE_PATH下创建以草稿ID命名的文件夹
        os.makedirs(draft_path, exist_ok=True)

        # 保存draft.json文件
        draft_json_path = os.path.join(draft_path, "draft.json")
        with open(draft_json_path, "w", encoding="utf-8") as f:
            json.dump(draft_data, f, ensure_ascii=False, indent=4)

        # 添加草稿索引记录

        draft_info = {
            "draft_name": draft_name,
            "created_time": datetime.datetime.now().isoformat(),
            "width": width,
            "height": height,
            "fps": fps
        }
        index_manager.add_draft_mapping(draft_id, draft_info)

        return draft_data

    @mcp.tool()
    def export_draft(draft_id: str, output_path: Optional[str] = None) -> ToolResponse:
        """
        导出草稿为剪映项目

        Args:
            draft_id: 草稿ID，必须是已存在的草稿
            output_path: 导出路径（可选）
        """
        try:
            # 验证草稿是否存在
            draft_data_path = os.path.join(SAVE_PATH, draft_id)
            if not os.path.exists(draft_data_path):
                return ToolResponse(
                    success=False,
                    message=f"草稿不存在: {draft_id}"
                )

            # 验证草稿数据文件是否存在
            draft_json_path = os.path.join(draft_data_path, "draft.json")
            if not os.path.exists(draft_json_path):
                return ToolResponse(
                    success=False,
                    message=f"草稿数据文件不存在: {draft_id}/draft.json"
                )

            # 创建导出器
            exporter = ExportDraft(output_path)

            # 执行导出
            export_result = exporter.export(draft_id)

            if export_result and isinstance(export_result, dict):
                return ToolResponse(
                    success=True,
                    message="草稿导出成功",
                    data={
                        "draft_id": draft_id,
                        "output_path": export_result.get("output"),
                        "draft_name": export_result.get("draft_name"),
                        "export_logs": export_result.get("export_logs", []),
                        "summary": export_result.get("summary", {}),
                        "processing_details": {
                            "total_operations": len(export_result.get("export_logs", [])),
                            "successful_operations": len([log for log in export_result.get("export_logs", []) if log.get("level") == "info"]),
                            "warnings": len([log for log in export_result.get("export_logs", []) if log.get("level") == "warning"]),
                            "errors": len([log for log in export_result.get("export_logs", []) if log.get("level") == "error"])
                        }
                    }
                )
            else:
                return ToolResponse(
                    success=False,
                    message="草稿导出失败，请检查草稿数据完整性"
                )

        except FileNotFoundError as e:
            return ToolResponse(
                success=False,
                message=f"文件不存在: {str(e)}"
            )
        except Exception as e:
            return ToolResponse(
                success=False,
                message=f"导出失败: {str(e)}"
            )
