<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="修改端口以及导出路径问题">
      <change afterPath="$PROJECT_DIR$/MCP Document/1_Introduction.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/2_Architecture Overview.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/3_mcp python sdk.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/4_mcp server and client.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/5_mcp study.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/draft_content.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/draft_meta_info.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/material/audio.mp3" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/material/video.mp4" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.env" beforeDir="false" afterPath="$PROJECT_DIR$/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/fastapi_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/fastapi_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/jianying/audio.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/jianying/audio.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/jianying/export.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/jianying/export.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/jianying/video.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/jianying/video.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/server.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/server.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="31GaUk5isUZ2riHe1n2owt57WVR" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.debug.executor": "Run",
    "Python.export.executor": "Debug",
    "Python.fastapi_server.executor": "Run",
    "Python.server.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/pythonProject/MyProject/jianying-mcp/data/output",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "advanced.settings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\jianying-mcp\data\output" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp\jianyingdraft\zip" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp\MCP Document" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\jianying-mcp\data" />
    </key>
  </component>
  <component name="RunManager" selected="Python.export">
    <configuration name="debug" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/debug.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="export" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft/jianying" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/jianying/export.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="fastapi_server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/fastapi_server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.export" />
        <item itemvalue="Python.server" />
        <item itemvalue="Python.fastapi_server" />
        <item itemvalue="Python.debug" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="" />
      <created>1755152881347</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755152881347</updated>
      <workItem from="1755152882429" duration="12635000" />
      <workItem from="1755169457162" duration="8000" />
      <workItem from="1755223507122" duration="966000" />
      <workItem from="1755244726780" duration="4872000" />
    </task>
    <task id="LOCAL-00001" summary="第一次提交">
      <option name="closed" value="true" />
      <created>1755158000197</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755158000197</updated>
    </task>
    <task id="LOCAL-00002" summary="mcp基本完成">
      <option name="closed" value="true" />
      <created>1755161992088</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755161992088</updated>
    </task>
    <task id="LOCAL-00003" summary="sse">
      <option name="closed" value="true" />
      <created>1755244767634</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755244767634</updated>
    </task>
    <task id="LOCAL-00004" summary="修改端口以及导出路径问题">
      <option name="closed" value="true" />
      <created>1755248421472</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755248421472</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第一次提交" />
    <MESSAGE value="mcp基本完成" />
    <MESSAGE value="sse" />
    <MESSAGE value="修改端口以及导出路径问题" />
    <option name="LAST_COMMIT_MESSAGE" value="修改端口以及导出路径问题" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jianyingdraft/jianying/export.py</url>
          <line>108</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jianyingdraft/jianying/export.py</url>
          <line>852</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/jianyingdraft/jianying/export.py</url>
          <line>102</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/jianying_mcp$fastapi_server.coverage" NAME="fastapi_server 覆盖结果" MODIFIED="1755161276208" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
    <SUITE FILE_PATH="coverage/jianying_mcp$export.coverage" NAME="export 覆盖结果" MODIFIED="1755247473735" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft/jianying" />
    <SUITE FILE_PATH="coverage/jianying_mcp$server.coverage" NAME="server 覆盖结果" MODIFIED="1755245320146" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
    <SUITE FILE_PATH="coverage/jianying_mcp$debug.coverage" NAME="debug 覆盖结果" MODIFIED="1755155797257" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
  </component>
</project>