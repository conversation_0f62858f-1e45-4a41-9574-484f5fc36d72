<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="第一次提交">
      <change afterPath="$PROJECT_DIR$/MCP Document/1_Introduction.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/2_Architecture Overview.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/3_mcp python sdk.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/4_mcp server and client.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/5_mcp study.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/jianyingdraft/debug.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/audio_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/audio_tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/draft_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/draft_tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/text_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/text_tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/video_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/video_tool.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="31GaUk5isUZ2riHe1n2owt57WVR" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.debug.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/pythonProject/MyProject/jianying-mcp/MCP Document",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\jianying-mcp\MCP Document" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="debug" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/debug.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.debug" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="" />
      <created>1755152881347</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755152881347</updated>
      <workItem from="1755152882429" duration="6135000" />
    </task>
    <task id="LOCAL-00001" summary="第一次提交">
      <option name="closed" value="true" />
      <created>1755158000197</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755158000197</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第一次提交" />
    <option name="LAST_COMMIT_MESSAGE" value="第一次提交" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/jianying_mcp$debug.coverage" NAME="debug 覆盖结果" MODIFIED="1755155797257" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
  </component>
</project>