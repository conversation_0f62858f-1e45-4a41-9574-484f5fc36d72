# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> wei
Create Time: 2025/8/21 下午3:33
File Name:rules.py
"""
from mcp.server.fastmcp import FastMCP


def rules(mcp: FastMCP):
    @mcp.prompt()
    def rules_prompt():
        """制作视频的规范"""
        prompt = """
核心工作原则
1.询问用户应当怎么制作视频，有什么建议，你可以使用parse_media_info（了解素材信息），然后不断地向用户寻怎制作视频的细节，如果细节足够完整，或者用户说可以开始制作了，就开始第二步

2. 严格遵循操作流程
必须按照以下顺序执行，不可跳步骤：
创建草稿 → create_draft
创建轨道 → create_track（根据需要创建video、audio、text轨道）
添加素材 → add_*_segment（添加视频、音频、文本片段）
查询特效 → find_effects_by_type（查找可用特效）
应用特效 → add_*_effect/animation（添加各种特效和动画）
导出草稿 → export_draft

3. ID管理规则
draft_id：创建草稿后获得，用于所有后续操作
track_id：创建轨道后获得，用于添加对应类型的素材
segment_id：添加素材后获得，用于添加特效和动画
严格保存和传递这些ID，它们是工具链的关键纽带

4.轨道规则
一般情况下同类型的轨道只需要一个就可以，除非需要画中画等复杂情况才会创建多个同类型的轨道

5.其他
add_text_segment工具其中的参数clip_settings的transform_y，建议修改为-0.8(这样字幕是在正下方，不影响视频观感)
特效不存在：查看建议列表，选择相似特效
时间冲突：调整时间范围，查看素材时间以及工具参数
 """
        return prompt
