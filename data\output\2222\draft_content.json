{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 7000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "91E08AC5-22FB-47e2-9AA0-7DC300FAEA2B", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "speeds": [], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"id": "a4bc2b0603114529bc0295c90d5b82a5", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 14], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"这是jianying mcp\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}, {"id": "e11c402a4cc046368fe8510be12145ab", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 11], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"哈哈哈哈哈哈哈哈哈哈哈\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}, {"id": "36c10b63be7247bb914322dc56a94fc6", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 4], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"niha\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "cbe9454d5b034942a10040d76185b4cf", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "a1a4037421ce44d4a779ed0c2827d7ce", "material_id": "a4bc2b0603114529bc0295c90d5b82a5", "target_timerange": {"start": 0, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["8e69e791610b4039ac956d8f16f052db"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0, "y": -1}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "62a00ac6d4a444ad8bc2c8299b13bdb2", "material_id": "e11c402a4cc046368fe8510be12145ab", "target_timerange": {"start": 5000000, "duration": 1000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["b5522c8b21e640f180ac0f92fee1683f"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}, {"attribute": 0, "flag": 0, "id": "054222386060495380d773ce52f194af", "is_default_name": false, "name": "text2", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "cc4d6c9999cb4feb853fc66f47ac056a", "material_id": "36c10b63be7247bb914322dc56a94fc6", "target_timerange": {"start": 6000000, "duration": 1000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["3cc843386f6c49c9b1d5463c738e1076"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0, "y": -0.6}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}], "update_time": 0, "version": 360000}